
import React from 'react';

interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
}

export const Card: React.FC<CardProps> = ({ title, children, className, titleClassName }) => {
  return (
    <div className={`bg-base-100 shadow-xl rounded-lg p-4 md:p-6 ${className}`}>
      {title && <h2 className={`text-xl font-semibold mb-4 text-neutral-content ${titleClassName}`}>{title}</h2>}
      {children}
    </div>
  );
};
