
import React from 'react';
import { Card } from '../common/Card';
import { Button } from '../common/Button';
import { ShieldCheckIcon } from '../icons';

interface RiskManagementViewProps {
  isEmergencyStopActive: boolean;
  onToggleEmergencyStop: () => void;
}

const ParameterDisplay: React.FC<{ label: string, value: string, unit?: string, description?: string }> = ({ label, value, unit, description }) => (
    <div className="p-3 bg-base-100 rounded-md">
        <div className="flex justify-between items-center">
            <span className="text-md font-medium text-gray-300">{label}</span>
            <span className="text-lg font-semibold text-accent">{value}{unit && <span className="text-sm ml-1">{unit}</span>}</span>
        </div>
        {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
    </div>
);

export const RiskManagementView: React.FC<RiskManagementViewProps> = ({ isEmergencyStopActive, onToggleEmergencyStop }) => {
  return (
    <div className="space-y-6">
      <Card title="Risk Management Controls & Parameters">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <ParameterDisplay label="Max Position Size per Trade" value="1.5" unit="%" description="Percentage of total capital allocated per trade." />
            <ParameterDisplay label="Global Exposure Limit (ETH)" value="10" unit="ETH" description="Maximum exposure to any single asset."/>
            <ParameterDisplay label="Circuit Breaker Threshold" value="5" unit="% price swing / 5min" description="Triggers trading halt on extreme volatility."/>
            <ParameterDisplay label="Slippage Tolerance" value="0.5" unit="%" description="Maximum allowed price slippage for trades."/>
        </div>

        <Card title="Emergency Stop" className="bg-neutral border border-error/50">
            <div className="flex flex-col items-center text-center p-4">
                <ShieldCheckIcon className={`w-16 h-16 mb-4 ${isEmergencyStopActive ? 'text-error animate-pulse-fast' : 'text-success'}`} />
                <p className="text-lg font-semibold mb-2">
                    Trading Status: {isEmergencyStopActive ? 
                    <span className="text-error">EMERGENCY STOP ACTIVE</span> : 
                    <span className="text-success">Operational</span>}
                </p>
                <p className="text-sm text-gray-400 mb-4">
                    {isEmergencyStopActive 
                    ? "All automated trading operations are currently halted. Manual intervention may be required to resume." 
                    : "Automated trading is active. Activate emergency stop only in critical situations."}
                </p>
                <Button 
                    variant={isEmergencyStopActive ? "success" : "error"} 
                    size="lg"
                    onClick={onToggleEmergencyStop}
                >
                    {isEmergencyStopActive ? "Deactivate Emergency Stop" : "Activate Emergency Stop"}
                </Button>
                {isEmergencyStopActive && <p className="text-xs text-warning mt-3">Deactivate only when the critical issue is resolved.</p>}
            </div>
        </Card>
      </Card>
    </div>
  );
};
