// Simple backend server using native Node.js modules
import http from 'http';
import url from 'url';

console.log('Starting simple MEV Arbitrage Bot backend...');

// Mock data
const mockOpportunities = [
  {
    id: 'opp_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    potentialProfit: 125.50,
    profitPercentage: 2.1,
    timestamp: Date.now(),
    network: 'ethereum',
    confidence: 85,
    slippage: 0.5
  },
  {
    id: 'opp_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    potentialProfit: 89.25,
    profitPercentage: 1.8,
    timestamp: Date.now() - 30000,
    network: 'ethereum',
    confidence: 78,
    slippage: 0.8
  }
];

const mockTrades = [
  {
    id: 'trade_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    executedProfit: 118.25,
    gasFees: 7.25,
    status: 'success',
    timestamp: Date.now() - 300000,
    network: 'ethereum',
    txHash: '0x1234567890abcdef'
  },
  {
    id: 'trade_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    executedProfit: 82.50,
    gasFees: 12.75,
    status: 'success',
    timestamp: Date.now() - 600000,
    network: 'ethereum',
    txHash: '0xabcdef1234567890'
  }
];

const mockTokens = [
  {
    id: 'ethereum_******************************************',
    name: 'Ethereum',
    symbol: 'ETH',
    address: '******************************************',
    liquidity: 1000000,
    safetyScore: 100,
    isWhitelisted: true,
    network: 'ethereum',
    decimals: 18,
    totalSupply: '120000000000000000000000000',
    lastUpdated: Date.now()
  },
  {
    id: 'ethereum_0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    name: 'USD Coin',
    symbol: 'USDC',
    address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    liquidity: 500000,
    safetyScore: 95,
    isWhitelisted: true,
    network: 'ethereum',
    decimals: 6,
    totalSupply: '50000000000000',
    lastUpdated: Date.now()
  }
];

const mockMetrics = {
  totalTrades: 45,
  successfulTrades: 39,
  totalProfit: 2850.75,
  netProfit: 2650.50,
  winRate: 86.7,
  avgProfit: 67.96,
  dailyVolume: 125000
};

// Helper function to send JSON response
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // Routes
  if (path === '/health') {
    sendJSON(res, {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        backend: true,
        websocket: false
      }
    });
  } else if (path === '/api/opportunities') {
    sendJSON(res, {
      success: true,
      data: mockOpportunities,
      count: mockOpportunities.length
    });
  } else if (path === '/api/trades') {
    sendJSON(res, {
      success: true,
      data: mockTrades,
      count: mockTrades.length
    });
  } else if (path === '/api/tokens') {
    sendJSON(res, {
      success: true,
      data: mockTokens,
      count: mockTokens.length
    });
  } else if (path === '/api/analytics/performance') {
    sendJSON(res, {
      success: true,
      data: mockMetrics
    });
  } else if (path === '/api/system/health') {
    sendJSON(res, {
      success: true,
      data: {
        isHealthy: true,
        emergencyStop: false,
        riskMetrics: {
          totalExposure: 0,
          dailyPnL: 250.75,
          maxDrawdown: 0,
          volatility: 15.2,
          winRate: 86.7
        },
        activeAlerts: 0,
        criticalAlerts: 0
      }
    });
  } else {
    sendJSON(res, {
      success: false,
      error: 'Route not found'
    }, 404);
  }
});

// Start server
const PORT = 3001;
server.listen(PORT, () => {
  console.log(`✓ Simple backend server running on http://localhost:${PORT}`);
  console.log(`✓ Health check: http://localhost:${PORT}/health`);
  console.log(`✓ API endpoints available:`);
  console.log(`  - GET /api/opportunities`);
  console.log(`  - GET /api/trades`);
  console.log(`  - GET /api/tokens`);
  console.log(`  - GET /api/analytics/performance`);
  console.log(`  - GET /api/system/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down backend...');
  server.close();
});

process.on('SIGINT', () => {
  console.log('Shutting down backend...');
  server.close();
});
