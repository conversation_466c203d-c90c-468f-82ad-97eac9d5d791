import { useState, useEffect, useCallback } from 'react';
import { backendService, BackendOpportunity, BackendTrade, BackendToken, BackendMetrics } from '../services/backendService';
import { ArbitrageOpportunity, Trade, Token, KPI, ArbitrageType, TradeStatus } from '../types';

export function useBackendIntegration() {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [backendHealthy, setBackendHealthy] = useState(false);

  // Backend data
  const [backendOpportunities, setBackendOpportunities] = useState<ArbitrageOpportunity[]>([]);
  const [backendTrades, setBackendTrades] = useState<Trade[]>([]);
  const [backendTokens, setBackendTokens] = useState<Token[]>([]);
  const [backendMetrics, setBackendMetrics] = useState<KPI | null>(null);

  // Convert backend opportunity to frontend format
  const convertOpportunity = useCallback((backendOpp: BackendOpportunity): ArbitrageOpportunity => ({
    id: backendOpp.id,
    type: backendOpp.type as ArbitrageType,
    assets: backendOpp.assets,
    exchanges: backendOpp.exchanges,
    potentialProfit: backendOpp.potentialProfit,
    timestamp: backendOpp.timestamp,
    network: backendOpp.network
  }), []);

  // Convert backend trade to frontend format
  const convertTrade = useCallback((backendTrade: BackendTrade): Trade => ({
    id: backendTrade.id,
    opportunityId: '', // Would need to be included in backend response
    type: backendTrade.type as ArbitrageType,
    assets: backendTrade.assets,
    exchanges: backendTrade.exchanges,
    executedProfit: backendTrade.executedProfit,
    gasFees: backendTrade.gasFees,
    status: backendTrade.status as TradeStatus,
    timestamp: backendTrade.timestamp,
    network: backendTrade.network,
    txHash: backendTrade.txHash
  }), []);

  // Convert backend token to frontend format
  const convertToken = useCallback((backendToken: BackendToken): Token => ({
    id: backendToken.id,
    name: backendToken.name,
    symbol: backendToken.symbol,
    address: backendToken.address,
    liquidity: backendToken.liquidity,
    safetyScore: backendToken.safetyScore,
    isWhitelisted: backendToken.isWhitelisted,
    network: backendToken.network
  }), []);

  // Convert backend metrics to frontend format
  const convertMetrics = useCallback((backendMetrics: BackendMetrics): KPI => ({
    totalProfit: backendMetrics.netProfit,
    roi: backendMetrics.netProfit > 0 ? (backendMetrics.netProfit / 10000) * 100 : 0, // Assuming 10k initial capital
    winRate: backendMetrics.winRate,
    activeTrades: 0, // Would need to be calculated separately
    dailyVolume: backendMetrics.dailyVolume
  }), []);

  // Load initial data from backend
  const loadBackendData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if backend is healthy
      const healthy = await backendService.isBackendHealthy();
      setBackendHealthy(healthy);

      if (!healthy) {
        setError('Backend is not available');
        return;
      }

      // Fetch all data in parallel
      const [opportunities, trades, tokens, metrics] = await Promise.all([
        backendService.fetchOpportunities(20),
        backendService.fetchTrades(50),
        backendService.fetchTokens(),
        backendService.fetchMetrics()
      ]);

      // Convert and set data
      setBackendOpportunities(opportunities.map(convertOpportunity));
      setBackendTrades(trades.map(convertTrade));
      setBackendTokens(tokens.map(convertToken));
      
      if (metrics) {
        setBackendMetrics(convertMetrics(metrics));
      }

    } catch (error) {
      console.error('Error loading backend data:', error);
      setError('Failed to load data from backend');
    } finally {
      setIsLoading(false);
    }
  }, [convertOpportunity, convertTrade, convertToken, convertMetrics]);

  // Initialize WebSocket connection
  const initializeWebSocket = useCallback(() => {
    backendService.initWebSocket({
      onConnect: () => {
        setIsConnected(true);
        setError(null);
        console.log('Connected to backend WebSocket');
      },
      onDisconnect: () => {
        setIsConnected(false);
        console.log('Disconnected from backend WebSocket');
      },
      onError: (error) => {
        setError('WebSocket connection error');
        console.error('WebSocket error:', error);
      },
      onOpportunity: (opportunity) => {
        const convertedOpp = convertOpportunity(opportunity);
        setBackendOpportunities(prev => [convertedOpp, ...prev.slice(0, 19)]);
      },
      onTrade: (trade) => {
        const convertedTrade = convertTrade(trade);
        setBackendTrades(prev => [convertedTrade, ...prev.slice(0, 49)]);
        
        // Update metrics when new trade comes in
        if (backendMetrics) {
          setBackendMetrics(prev => {
            if (!prev) return prev;
            
            const isSuccessful = convertedTrade.status === TradeStatus.SUCCESS;
            const newTotalTrades = prev.activeTrades + 1; // Simplified
            const newWinRate = isSuccessful 
              ? ((prev.winRate * (newTotalTrades - 1)) + 100) / newTotalTrades
              : (prev.winRate * (newTotalTrades - 1)) / newTotalTrades;
            
            return {
              ...prev,
              totalProfit: prev.totalProfit + (isSuccessful ? convertedTrade.executedProfit : 0),
              winRate: newWinRate,
              activeTrades: newTotalTrades
            };
          });
        }
      },
      onSystemUpdate: (data) => {
        console.log('System update received:', data);
        // Handle system updates (emergency stop, alerts, etc.)
      }
    });
  }, [convertOpportunity, convertTrade, backendMetrics]);

  // Backend API methods
  const addTokenToWhitelist = useCallback(async (token: {
    name: string;
    symbol: string;
    address: string;
    network: string;
    decimals?: number;
  }) => {
    try {
      const success = await backendService.addTokenToWhitelist(token);
      if (success) {
        // Refresh tokens
        const tokens = await backendService.fetchTokens();
        setBackendTokens(tokens.map(convertToken));
      }
      return success;
    } catch (error) {
      console.error('Error adding token to whitelist:', error);
      return false;
    }
  }, [convertToken]);

  const addTokenToBlacklist = useCallback(async (token: {
    address: string;
    network: string;
    reason: string;
  }) => {
    try {
      const success = await backendService.addTokenToBlacklist(token);
      if (success) {
        // Refresh tokens
        const tokens = await backendService.fetchTokens();
        setBackendTokens(tokens.map(convertToken));
      }
      return success;
    } catch (error) {
      console.error('Error adding token to blacklist:', error);
      return false;
    }
  }, [convertToken]);

  const toggleEmergencyStop = useCallback(async () => {
    try {
      const success = await backendService.toggleEmergencyStop();
      if (success) {
        // Refresh system health
        const health = await backendService.fetchSystemHealth();
        console.log('Emergency stop toggled, new health:', health);
      }
      return success;
    } catch (error) {
      console.error('Error toggling emergency stop:', error);
      return false;
    }
  }, []);

  const refreshData = useCallback(() => {
    loadBackendData();
  }, [loadBackendData]);

  // Initialize on mount
  useEffect(() => {
    loadBackendData();
    initializeWebSocket();

    // Refresh data every 30 seconds
    const interval = setInterval(loadBackendData, 30000);

    return () => {
      clearInterval(interval);
      backendService.closeWebSocket();
    };
  }, [loadBackendData, initializeWebSocket]);

  return {
    // Connection status
    isConnected,
    isLoading,
    error,
    backendHealthy,
    
    // Data
    opportunities: backendOpportunities,
    trades: backendTrades,
    tokens: backendTokens,
    metrics: backendMetrics,
    
    // Actions
    addTokenToWhitelist,
    addTokenToBlacklist,
    toggleEmergencyStop,
    refreshData
  };
}
