
import React from 'react';
import { ViewId, NavItem } from '../types';
import { DashboardIcon, LightBulbIcon, TableIcon, TokenIcon, SystemStatusIcon, ShieldCheckIcon, CogIcon, SparklesIcon } from './icons';

interface LayoutProps {
  currentView: ViewId;
  onNavigate: (viewId: ViewId) => void;
  children: React.ReactNode;
}

const navigationItems: NavItem[] = [
  { name: 'Dashboard', viewId: ViewId.DASHBOARD, icon: <DashboardIcon /> },
  { name: 'Opportunities', viewId: ViewId.OPPORTUNITIES, icon: <LightBulbIcon /> },
  { name: 'Trade Log', viewId: ViewId.TRADES, icon: <TableIcon /> },
  { name: 'Token Management', viewId: ViewId.TOKENS, icon: <TokenIcon /> },
  { name: 'System Status', viewId: ViewId.SYSTEM_STATUS, icon: <SystemStatusIcon /> },
  { name: 'Risk Management', viewId: ViewId.RISK_MANAGEMENT, icon: <ShieldCheckIcon /> },
  { name: 'Configuration', viewId: ViewId.CONFIGURATION, icon: <CogIcon /> },
  { name: 'Strategy Insights', viewId: ViewId.STRATEGY_INSIGHTS, icon: <SparklesIcon /> },
];

export const Layout: React.FC<LayoutProps> = ({ currentView, onNavigate, children }) => {
  return (
    <div className="flex h-screen bg-neutral text-neutral-content">
      {/* Sidebar */}
      <aside className="w-64 bg-base-100 p-4 space-y-2 border-r border-secondary overflow-y-auto">
        <h1 className="text-2xl font-bold text-primary mb-6 px-2">MEV Bot</h1>
        <nav>
          {navigationItems.map((item) => (
            <button
              key={item.viewId}
              onClick={() => onNavigate(item.viewId)}
              className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-md text-sm font-medium transition-colors
                ${currentView === item.viewId 
                  ? 'bg-primary text-white' 
                  : 'text-neutral-content hover:bg-secondary hover:text-white'
                }`}
            >
              {React.cloneElement(item.icon, { className: "w-5 h-5" })}
              <span>{item.name}</span>
            </button>
          ))}
        </nav>
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-base-100 p-4 shadow-md border-b border-secondary">
          <h2 className="text-xl font-semibold capitalize text-neutral-content">{currentView.replace('_', ' ')}</h2>
        </header>

        {/* Content area */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto p-4 md:p-6 lg:p-8 bg-neutral">
          {children}
        </main>
      </div>
    </div>
  );
};
