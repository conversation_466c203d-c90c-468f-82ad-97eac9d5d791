
import React, { useState } from 'react';
import { Card } from '../common/Card';
import { Button } from '../common/Button';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { generateText } from '../../services/geminiService';
import { SparklesIcon } from '../icons';

const suggestedPrompts = [
  "Explain the risks of MEV arbitrage.",
  "What are common MEV protection mechanisms besides Flashbots?",
  "Describe a triangular arbitrage strategy in DeFi.",
  "How do flash loans work in the context of MEV?",
  "What are key considerations for gas optimization in arbitrage smart contracts?"
];

export const StrategyInsightsView: React.FC = () => {
  const [prompt, setPrompt] = useState<string>('');
  const [insight, setInsight] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateInsight = async () => {
    if (!prompt.trim()) {
      setError("Please enter a prompt.");
      return;
    }
    setIsLoading(true);
    setError(null);
    setInsight('');
    try {
      const result = await generateText(`As an expert in MEV and DeFi arbitrage, provide insights on the following topic: "${prompt}". Keep the explanation concise and informative for someone familiar with blockchain concepts.`);
      setInsight(result);
    } catch (e: any) {
      setError(e.message || "Failed to generate insight.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestedPromptClick = (suggested: string) => {
    setPrompt(suggested);
    // Do not auto-submit, let user click the generate button
    // handleGenerateInsight(); 
  }

  return (
    <Card title="AI-Powered Strategy Insights" titleClassName="text-neutral-content">
      <div className="space-y-4">
        <div>
          <label htmlFor="prompt" className="block text-sm font-medium text-gray-300 mb-1">
            Ask about MEV, arbitrage strategies, or DeFi concepts:
          </label>
          <textarea
            id="prompt"
            rows={3}
            className="w-full p-2 rounded-md bg-neutral text-neutral-content border border-secondary focus:ring-primary focus:border-primary"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="e.g., Explain how Flashbots helps prevent front-running."
          />
        </div>
        
        <div className="mb-4">
            <p className="text-sm text-gray-400 mb-1">Or try a suggestion:</p>
            <div className="flex flex-wrap gap-2">
                {suggestedPrompts.map(p => (
                    <Button key={p} variant="ghost" size="sm" onClick={() => handleSuggestedPromptClick(p)}>
                        {p}
                    </Button>
                ))}
            </div>
        </div>

        <Button onClick={handleGenerateInsight} isLoading={isLoading} disabled={!prompt.trim()} className="flex items-center justify-center">
          <SparklesIcon className="w-5 h-5 mr-2" />
          Generate Insight
        </Button>

        {isLoading && <LoadingSpinner text="Generating insight with Gemini AI..." />}
        {error && <p className="text-error mt-2 p-2 bg-error/20 rounded-md">{error}</p>}
        
        {insight && !isLoading && (
          <div className="mt-4 p-4 bg-base-100 rounded-md border border-secondary">
            <h3 className="text-lg font-semibold text-primary mb-2">Generated Insight:</h3>
            <pre className="whitespace-pre-wrap text-neutral-content text-sm leading-relaxed">{insight}</pre>
          </div>
        )}
      </div>
    </Card>
  );
};
