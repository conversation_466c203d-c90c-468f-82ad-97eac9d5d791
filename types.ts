
import React from 'react';

export interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  liquidity: number;
  safetyScore: number; // 0-100
  isWhitelisted: boolean;
  network: string;
}

export enum ArbitrageType {
  INTRA_CHAIN = "Intra-chain",
  CROSS_CHAIN = "Cross-chain",
  TRIANGULAR = "Triangular",
}

export interface ArbitrageOpportunity {
  id: string;
  type: ArbitrageType;
  assets: string[]; // e.g., ["ETH", "USDC"]
  exchanges: string[]; // e.g., ["Uniswap", "Sushiswap"]
  potentialProfit: number;
  timestamp: number;
  network: string;
}

export enum TradeStatus {
  PENDING = "Pending",
  SUCCESS = "Success",
  FAILED = "Failed",
}

export interface Trade {
  id: string;
  opportunityId: string;
  type: ArbitrageType;
  assets: string[];
  exchanges: string[];
  executedProfit: number;
  gasFees: number;
  status: TradeStatus;
  timestamp: number;
  network: string;
}

export interface KPI {
  totalProfit: number;
  roi: number; // percentage
  winRate: number; // percentage
  activeTrades: number;
  dailyVolume: number;
}

export enum SystemComponentType {
  BACKEND_SERVICE = "Backend Service",
  SMART_CONTRACT = "Smart Contract",
  EXTERNAL_INTEGRATION = "External Integration",
  DATA_LAYER = "Data Layer"
}

export enum ComponentStatus {
  ONLINE = "Online",
  WARNING = "Warning",
  OFFLINE = "Offline",
  DEGRADED = "Degraded"
}

export interface SystemComponent {
  id: string;
  name: string;
  type: SystemComponentType;
  status: ComponentStatus;
}

export interface NavItem {
  name: string;
  viewId: ViewId;
  icon: React.ReactElement<{ className?: string }>; // Changed from React.ReactNode
}

export enum ViewId {
  DASHBOARD = "dashboard",
  OPPORTUNITIES = "opportunities",
  TRADES = "trades",
  TOKENS = "tokens",
  SYSTEM_STATUS = "system_status",
  STRATEGY_INSIGHTS = "strategy_insights",
  RISK_MANAGEMENT = "risk_management",
  CONFIGURATION = "configuration"
}
