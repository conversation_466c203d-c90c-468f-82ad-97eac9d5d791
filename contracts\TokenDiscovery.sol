// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "./interfaces/IERC20.sol";

/**
 * @title TokenDiscovery
 * @dev Contract for managing token whitelists and blacklists for arbitrage operations
 */
contract TokenDiscovery is Ownable, ReentrancyGuard {
    
    struct TokenInfo {
        address tokenAddress;
        string name;
        string symbol;
        uint256 minLiquidity;
        uint8 safetyScore; // 0-100
        bool isWhitelisted;
        bool isBlacklisted;
        uint256 addedTimestamp;
    }
    
    mapping(address => TokenInfo) public tokens;
    mapping(address => bool) public authorizedCallers;
    
    address[] public whitelistedTokens;
    address[] public blacklistedTokens;
    
    uint256 public constant MIN_LIQUIDITY_THRESHOLD = 2 ether; // Equivalent to 2 ETH
    uint256 public constant MIN_SAFETY_SCORE = 70;
    
    event TokenWhitelisted(address indexed token, string name, string symbol);
    event TokenBlacklisted(address indexed token, string reason);
    event TokenRemoved(address indexed token);
    event AuthorizedCallerAdded(address indexed caller);
    event AuthorizedCallerRemoved(address indexed caller);
    
    modifier onlyAuthorized() {
        require(authorizedCallers[msg.sender] || msg.sender == owner(), "Not authorized");
        _;
    }
    
    constructor() {
        authorizedCallers[msg.sender] = true;
    }
    
    /**
     * @dev Add a token to the whitelist
     */
    function addToWhitelist(
        address tokenAddress,
        string memory name,
        string memory symbol,
        uint256 minLiquidity,
        uint8 safetyScore
    ) external onlyAuthorized {
        require(tokenAddress != address(0), "Invalid token address");
        require(!tokens[tokenAddress].isBlacklisted, "Token is blacklisted");
        require(minLiquidity >= MIN_LIQUIDITY_THRESHOLD, "Insufficient liquidity");
        require(safetyScore >= MIN_SAFETY_SCORE, "Safety score too low");
        
        if (!tokens[tokenAddress].isWhitelisted) {
            whitelistedTokens.push(tokenAddress);
        }
        
        tokens[tokenAddress] = TokenInfo({
            tokenAddress: tokenAddress,
            name: name,
            symbol: symbol,
            minLiquidity: minLiquidity,
            safetyScore: safetyScore,
            isWhitelisted: true,
            isBlacklisted: false,
            addedTimestamp: block.timestamp
        });
        
        emit TokenWhitelisted(tokenAddress, name, symbol);
    }
    
    /**
     * @dev Add a token to the blacklist
     */
    function addToBlacklist(address tokenAddress, string memory reason) external onlyAuthorized {
        require(tokenAddress != address(0), "Invalid token address");
        
        if (tokens[tokenAddress].isWhitelisted) {
            _removeFromWhitelist(tokenAddress);
        }
        
        if (!tokens[tokenAddress].isBlacklisted) {
            blacklistedTokens.push(tokenAddress);
        }
        
        tokens[tokenAddress].isBlacklisted = true;
        tokens[tokenAddress].isWhitelisted = false;
        
        emit TokenBlacklisted(tokenAddress, reason);
    }
    
    /**
     * @dev Check if a token is whitelisted and meets criteria
     */
    function isTokenValid(address tokenAddress) external view returns (bool) {
        TokenInfo memory token = tokens[tokenAddress];
        return token.isWhitelisted && 
               !token.isBlacklisted && 
               token.safetyScore >= MIN_SAFETY_SCORE;
    }
    
    /**
     * @dev Get all whitelisted tokens
     */
    function getWhitelistedTokens() external view returns (address[] memory) {
        return whitelistedTokens;
    }
    
    /**
     * @dev Get token information
     */
    function getTokenInfo(address tokenAddress) external view returns (TokenInfo memory) {
        return tokens[tokenAddress];
    }
    
    /**
     * @dev Add authorized caller
     */
    function addAuthorizedCaller(address caller) external onlyOwner {
        authorizedCallers[caller] = true;
        emit AuthorizedCallerAdded(caller);
    }
    
    /**
     * @dev Remove authorized caller
     */
    function removeAuthorizedCaller(address caller) external onlyOwner {
        authorizedCallers[caller] = false;
        emit AuthorizedCallerRemoved(caller);
    }
    
    /**
     * @dev Internal function to remove token from whitelist array
     */
    function _removeFromWhitelist(address tokenAddress) internal {
        for (uint i = 0; i < whitelistedTokens.length; i++) {
            if (whitelistedTokens[i] == tokenAddress) {
                whitelistedTokens[i] = whitelistedTokens[whitelistedTokens.length - 1];
                whitelistedTokens.pop();
                break;
            }
        }
    }
}
