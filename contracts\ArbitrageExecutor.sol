// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "./interfaces/IERC20.sol";
import "./interfaces/IFlashLoanReceiver.sol";
import "./TokenDiscovery.sol";
import "./LiquidityChecker.sol";

/**
 * @title ArbitrageExecutor
 * @dev Main contract for executing arbitrage trades with flash loans
 */
contract ArbitrageExecutor is Ownable, ReentrancyGuard, Pausable, IFlashLoanReceiver {
    
    struct ArbitrageParams {
        address[] tokens;
        address[] exchanges;
        uint256[] amounts;
        uint256 minProfit;
        uint8 arbitrageType; // 0: intra-chain, 1: cross-chain, 2: triangular
        bytes routeData;
    }
    
    TokenDiscovery public immutable tokenDiscovery;
    LiquidityChecker public immutable liquidityChecker;
    
    mapping(address => bool) public authorizedExecutors;
    mapping(address => uint256) public executorNonces;
    
    uint256 public totalProfitGenerated;
    uint256 public totalTradesExecuted;
    uint256 public minProfitThreshold = 50 * 1e18; // 50 USD equivalent
    uint256 public maxSlippage = 500; // 5% in basis points
    
    event ArbitrageExecuted(
        bytes32 indexed tradeId,
        address indexed executor,
        uint256 profit,
        uint8 arbitrageType
    );
    
    event FlashLoanExecuted(
        address indexed asset,
        uint256 amount,
        uint256 premium,
        bool success
    );
    
    event ProfitWithdrawn(address indexed to, uint256 amount);
    
    modifier onlyAuthorizedExecutor() {
        require(authorizedExecutors[msg.sender] || msg.sender == owner(), "Not authorized executor");
        _;
    }
    
    constructor(address _tokenDiscovery, address _liquidityChecker) {
        tokenDiscovery = TokenDiscovery(_tokenDiscovery);
        liquidityChecker = LiquidityChecker(_liquidityChecker);
        authorizedExecutors[msg.sender] = true;
    }
    
    /**
     * @dev Execute arbitrage with flash loan
     */
    function executeArbitrage(
        ArbitrageParams calldata params,
        address flashLoanProvider,
        uint256 flashLoanAmount
    ) external onlyAuthorizedExecutor whenNotPaused nonReentrant {
        require(params.tokens.length >= 2, "Invalid token count");
        require(params.minProfit >= minProfitThreshold, "Profit below threshold");
        
        // Validate all tokens are whitelisted
        for (uint i = 0; i < params.tokens.length; i++) {
            require(tokenDiscovery.isTokenValid(params.tokens[i]), "Invalid token");
        }
        
        bytes32 tradeId = keccak256(abi.encodePacked(
            msg.sender,
            executorNonces[msg.sender]++,
            block.timestamp,
            params.tokens
        ));
        
        // Encode parameters for flash loan callback
        bytes memory flashLoanParams = abi.encode(tradeId, params);
        
        // Initiate flash loan
        _initiateFlashLoan(flashLoanProvider, params.tokens[0], flashLoanAmount, flashLoanParams);
    }
    
    /**
     * @dev Flash loan callback - executes the arbitrage logic
     */
    function executeOperation(
        address[] calldata assets,
        uint256[] calldata amounts,
        uint256[] calldata premiums,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        require(initiator == address(this), "Invalid initiator");
        
        (bytes32 tradeId, ArbitrageParams memory arbitrageParams) = abi.decode(params, (bytes32, ArbitrageParams));
        
        uint256 initialBalance = IERC20(assets[0]).balanceOf(address(this));
        uint256 amountOwing = amounts[0] + premiums[0];
        
        bool success = false;
        
        if (arbitrageParams.arbitrageType == 0) {
            success = _executeIntraChainArbitrage(arbitrageParams);
        } else if (arbitrageParams.arbitrageType == 1) {
            success = _executeCrossChainArbitrage(arbitrageParams);
        } else if (arbitrageParams.arbitrageType == 2) {
            success = _executeTriangularArbitrage(arbitrageParams);
        }
        
        uint256 finalBalance = IERC20(assets[0]).balanceOf(address(this));
        uint256 profit = finalBalance > initialBalance ? finalBalance - initialBalance : 0;
        
        require(finalBalance >= amountOwing, "Insufficient funds to repay loan");
        require(profit >= arbitrageParams.minProfit, "Profit below minimum threshold");
        
        // Approve flash loan repayment
        IERC20(assets[0]).approve(msg.sender, amountOwing);
        
        if (success && profit > 0) {
            totalProfitGenerated += profit - (amounts[0] + premiums[0]);
            totalTradesExecuted++;
            
            emit ArbitrageExecuted(tradeId, initiator, profit, arbitrageParams.arbitrageType);
        }
        
        emit FlashLoanExecuted(assets[0], amounts[0], premiums[0], success);
        
        return true;
    }
    
    /**
     * @dev Execute intra-chain arbitrage
     */
    function _executeIntraChainArbitrage(ArbitrageParams memory params) internal returns (bool) {
        // Implementation for intra-chain arbitrage
        // This would involve swapping tokens between different DEXs on the same chain
        
        for (uint i = 0; i < params.exchanges.length - 1; i++) {
            // Simulate token swap between exchanges
            // In real implementation, this would call actual DEX contracts
            
            address tokenIn = params.tokens[i];
            address tokenOut = params.tokens[i + 1];
            uint256 amountIn = params.amounts[i];
            
            // Check liquidity before swap
            // require(liquidityChecker.hasSufficientLiquidity(exchangePool, tokenIn, amountIn, 10), "Insufficient liquidity");
            
            // Execute swap (placeholder)
            // _swapTokens(params.exchanges[i], tokenIn, tokenOut, amountIn);
        }
        
        return true;
    }
    
    /**
     * @dev Execute cross-chain arbitrage
     */
    function _executeCrossChainArbitrage(ArbitrageParams memory params) internal returns (bool) {
        // Implementation for cross-chain arbitrage
        // This would involve bridging assets and executing trades on different chains
        
        // Placeholder implementation
        return true;
    }
    
    /**
     * @dev Execute triangular arbitrage
     */
    function _executeTriangularArbitrage(ArbitrageParams memory params) internal returns (bool) {
        require(params.tokens.length == 3, "Triangular arbitrage requires 3 tokens");
        
        // Implementation for triangular arbitrage
        // This would involve a cycle of 3 token swaps
        
        // Placeholder implementation
        return true;
    }
    
    /**
     * @dev Initiate flash loan from provider
     */
    function _initiateFlashLoan(
        address provider,
        address asset,
        uint256 amount,
        bytes memory params
    ) internal {
        // This would integrate with actual flash loan providers like Aave, dYdX, etc.
        // Placeholder implementation
        
        address[] memory assets = new address[](1);
        assets[0] = asset;
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;
        
        uint256[] memory premiums = new uint256[](1);
        premiums[0] = amount * 9 / 10000; // 0.09% fee
        
        // Simulate flash loan callback
        this.executeOperation(assets, amounts, premiums, address(this), params);
    }
    
    /**
     * @dev Add authorized executor
     */
    function addAuthorizedExecutor(address executor) external onlyOwner {
        authorizedExecutors[executor] = true;
    }
    
    /**
     * @dev Remove authorized executor
     */
    function removeAuthorizedExecutor(address executor) external onlyOwner {
        authorizedExecutors[executor] = false;
    }
    
    /**
     * @dev Update minimum profit threshold
     */
    function updateMinProfitThreshold(uint256 newThreshold) external onlyOwner {
        minProfitThreshold = newThreshold;
    }
    
    /**
     * @dev Emergency pause
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Withdraw profits
     */
    function withdrawProfits(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount > 0, "Invalid amount");
        
        // Implementation would transfer actual tokens
        emit ProfitWithdrawn(to, amount);
    }
    
    /**
     * @dev Get contract statistics
     */
    function getStats() external view returns (uint256 totalProfit, uint256 totalTrades) {
        return (totalProfitGenerated, totalTradesExecuted);
    }
}
