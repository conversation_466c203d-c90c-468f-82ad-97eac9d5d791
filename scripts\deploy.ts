import { ethers } from "hardhat";
import fs from 'fs';
import path from 'path';

async function main() {
  console.log("Starting deployment of MEV Arbitrage Bot contracts...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  console.log("Account balance:", (await deployer.provider.getBalance(deployer.address)).toString());

  // Deploy TokenDiscovery contract
  console.log("\nDeploying TokenDiscovery contract...");
  const TokenDiscovery = await ethers.getContractFactory("TokenDiscovery");
  const tokenDiscovery = await TokenDiscovery.deploy();
  await tokenDiscovery.waitForDeployment();
  const tokenDiscoveryAddress = await tokenDiscovery.getAddress();
  console.log("TokenDiscovery deployed to:", tokenDiscoveryAddress);

  // Deploy LiquidityChecker contract
  console.log("\nDeploying LiquidityChecker contract...");
  const LiquidityChecker = await ethers.getContractFactory("LiquidityChecker");
  const liquidityChecker = await LiquidityChecker.deploy();
  await liquidityChecker.waitForDeployment();
  const liquidityCheckerAddress = await liquidityChecker.getAddress();
  console.log("LiquidityChecker deployed to:", liquidityCheckerAddress);

  // Deploy ArbitrageExecutor contract
  console.log("\nDeploying ArbitrageExecutor contract...");
  const ArbitrageExecutor = await ethers.getContractFactory("ArbitrageExecutor");
  const arbitrageExecutor = await ArbitrageExecutor.deploy(
    tokenDiscoveryAddress,
    liquidityCheckerAddress
  );
  await arbitrageExecutor.waitForDeployment();
  const arbitrageExecutorAddress = await arbitrageExecutor.getAddress();
  console.log("ArbitrageExecutor deployed to:", arbitrageExecutorAddress);

  // Deploy Governance contract
  console.log("\nDeploying Governance contract...");
  const Governance = await ethers.getContractFactory("Governance");
  const governance = await Governance.deploy();
  await governance.waitForDeployment();
  const governanceAddress = await governance.getAddress();
  console.log("Governance deployed to:", governanceAddress);

  // Set up contract relationships
  console.log("\nSetting up contract relationships...");
  
  // Add ArbitrageExecutor as authorized caller to TokenDiscovery
  await tokenDiscovery.addAuthorizedCaller(arbitrageExecutorAddress);
  console.log("Added ArbitrageExecutor as authorized caller to TokenDiscovery");

  // Add ArbitrageExecutor as authorized executor
  await arbitrageExecutor.addAuthorizedExecutor(deployer.address);
  console.log("Added deployer as authorized executor");

  // Add some initial tokens to whitelist
  console.log("\nAdding initial tokens to whitelist...");
  
  // Add ETH (represented as zero address)
  await tokenDiscovery.addToWhitelist(
    "******************************************",
    "Ethereum",
    "ETH",
    ethers.parseEther("1000"), // 1000 ETH minimum liquidity
    100 // 100% safety score
  );
  console.log("Added ETH to whitelist");

  // Create deployment info
  const deploymentInfo = {
    network: await ethers.provider.getNetwork(),
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    contracts: {
      TokenDiscovery: {
        address: tokenDiscoveryAddress,
        constructorArgs: []
      },
      LiquidityChecker: {
        address: liquidityCheckerAddress,
        constructorArgs: []
      },
      ArbitrageExecutor: {
        address: arbitrageExecutorAddress,
        constructorArgs: [tokenDiscoveryAddress, liquidityCheckerAddress]
      },
      Governance: {
        address: governanceAddress,
        constructorArgs: []
      }
    },
    gasUsed: {
      // These would be filled with actual gas usage in a real deployment
      TokenDiscovery: "estimated",
      LiquidityChecker: "estimated", 
      ArbitrageExecutor: "estimated",
      Governance: "estimated"
    }
  };

  // Save deployment info
  const deploymentsDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  const deploymentFile = path.join(deploymentsDir, `deployment-${Date.now()}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\nDeployment info saved to: ${deploymentFile}`);

  // Save addresses for frontend
  const addressesFile = path.join(__dirname, '..', 'contract-addresses.json');
  const addresses = {
    TokenDiscovery: tokenDiscoveryAddress,
    LiquidityChecker: liquidityCheckerAddress,
    ArbitrageExecutor: arbitrageExecutorAddress,
    Governance: governanceAddress
  };
  fs.writeFileSync(addressesFile, JSON.stringify(addresses, null, 2));
  console.log(`Contract addresses saved to: ${addressesFile}`);

  console.log("\n=== Deployment Summary ===");
  console.log("TokenDiscovery:", tokenDiscoveryAddress);
  console.log("LiquidityChecker:", liquidityCheckerAddress);
  console.log("ArbitrageExecutor:", arbitrageExecutorAddress);
  console.log("Governance:", governanceAddress);
  console.log("\nDeployment completed successfully!");

  // Verify contracts (if on a public network)
  const network = await ethers.provider.getNetwork();
  if (network.chainId !== 1337n && network.chainId !== 31337n) { // Not local hardhat
    console.log("\nTo verify contracts on Etherscan, run:");
    console.log(`npx hardhat verify --network ${network.name} ${tokenDiscoveryAddress}`);
    console.log(`npx hardhat verify --network ${network.name} ${liquidityCheckerAddress}`);
    console.log(`npx hardhat verify --network ${network.name} ${arbitrageExecutorAddress} ${tokenDiscoveryAddress} ${liquidityCheckerAddress}`);
    console.log(`npx hardhat verify --network ${network.name} ${governanceAddress}`);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Deployment failed:", error);
    process.exit(1);
  });
