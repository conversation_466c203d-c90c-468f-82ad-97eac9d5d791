
import React, { useState } from 'react';
import { Token } from '../../types';
import { Card } from '../common/Card';
import { Button } from '../common/Button';
import { TokenIcon, ChevronDownIcon, ChevronUpIcon } from '../icons';

interface TokensViewProps {
  tokens: Token[];
  setTokens: React.Dispatch<React.SetStateAction<Token[]>>;
}

interface TokenRowProps {
  token: Token;
  onToggleWhitelist: (id: string) => void;
}

const TokenRow: React.FC<TokenRowProps> = ({ token, onToggleWhitelist }) => {
  const [expanded, setExpanded] = useState(false);
  return (
    <>
    <tr className="hover:bg-secondary/30 transition-colors">
      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-neutral-content">{token.name} ({token.symbol})</td>
      <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">{token.network}</td>
      <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">${token.liquidity.toLocaleString()}</td>
      <td className="px-4 py-3 whitespace-nowrap text-sm">
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
          token.safetyScore > 70 ? 'bg-success/30 text-success' : token.safetyScore > 40 ? 'bg-warning/30 text-warning' : 'bg-error/30 text-error'
        }`}>
          {token.safetyScore}/100
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-sm">
        <Button size="sm" variant={token.isWhitelisted ? 'error' : 'success'} onClick={() => onToggleWhitelist(token.id)}>
          {token.isWhitelisted ? 'Blacklist' : 'Whitelist'}
        </Button>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">
         <Button size="sm" variant="ghost" onClick={() => setExpanded(!expanded)}>
            {expanded ? <ChevronUpIcon className="w-4 h-4"/> : <ChevronDownIcon className="w-4 h-4"/>}
        </Button>
      </td>
    </tr>
    {expanded && (
        <tr className="bg-base-100">
            <td colSpan={6} className="p-4">
                <div className="text-sm text-gray-400">
                    <p><span className="font-semibold text-neutral-content">Address:</span> {token.address}</p>
                    <p><span className="font-semibold text-neutral-content">Status:</span> {token.isWhitelisted ? 'Whitelisted' : 'Blacklisted / Not Whitelisted'}</p>
                    <p className="mt-2 italic">Further token details and historical data could be shown here.</p>
                </div>
            </td>
        </tr>
    )}
    </>
  );
}


export const TokensView: React.FC<TokensViewProps> = ({ tokens, setTokens }) => {
  
  const handleToggleWhitelist = (id: string) => {
    setTokens(prevTokens => 
      prevTokens.map(t => t.id === id ? { ...t, isWhitelisted: !t.isWhitelisted } : t)
    );
  };

  const whitelistedTokens = tokens.filter(t => t.isWhitelisted);
  const otherTokens = tokens.filter(t => !t.isWhitelisted);

  return (
    <div className="space-y-6">
      <Card title="Token Management" className="max-h-[calc(100vh-10rem)] overflow-hidden flex flex-col">
        {tokens.length === 0 ? (
             <div className="flex flex-col items-center justify-center h-full text-gray-500 py-10">
                <TokenIcon className="w-16 h-16 mb-4" />
                <p className="text-xl">No tokens found.</p>
                <p>The system might still be initializing token discovery.</p>
            </div>
        ) : (
        <div className="overflow-x-auto flex-grow">
          <h3 className="text-lg font-semibold p-2 text-neutral-content">Whitelisted Tokens</h3>
          <table className="min-w-full divide-y divide-secondary mb-6">
            <thead className="bg-base-100 sticky top-0">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Token</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Network</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Liquidity</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Safety Score</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Action</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Details</th>
              </tr>
            </thead>
            <tbody className="bg-neutral divide-y divide-secondary overflow-y-auto">
              {whitelistedTokens.map((token) => <TokenRow key={token.id} token={token} onToggleWhitelist={handleToggleWhitelist} />)}
            </tbody>
          </table>

          <h3 className="text-lg font-semibold p-2 text-neutral-content">Other Tokens (Blacklisted / Not Whitelisted)</h3>
           <table className="min-w-full divide-y divide-secondary">
            <thead className="bg-base-100 sticky top-0">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Token</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Network</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Liquidity</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Safety Score</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Action</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Details</th>
              </tr>
            </thead>
            <tbody className="bg-neutral divide-y divide-secondary overflow-y-auto">
              {otherTokens.map((token) => <TokenRow key={token.id} token={token} onToggleWhitelist={handleToggleWhitelist} />)}
            </tbody>
          </table>
        </div>
        )}
      </Card>
    </div>
  );
};
