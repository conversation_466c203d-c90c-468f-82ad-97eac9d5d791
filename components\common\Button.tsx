
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'accent' | 'error' | 'success' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  className = '',
  ...props
}) => {
  const baseStyle = "font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-150 ease-in-out";
  
  let variantStyle = "";
  switch (variant) {
    case 'primary':
      variantStyle = "bg-primary text-white hover:bg-blue-700 focus:ring-primary";
      break;
    case 'secondary':
      variantStyle = "bg-secondary text-white hover:bg-gray-700 focus:ring-secondary";
      break;
    case 'accent':
      variantStyle = "bg-accent text-neutral hover:bg-amber-500 focus:ring-accent";
      break;
    case 'error':
      variantStyle = "bg-error text-white hover:bg-red-700 focus:ring-error";
      break;
    case 'success':
      variantStyle = "bg-success text-white hover:bg-green-700 focus:ring-success";
      break;
    case 'ghost':
      variantStyle = "bg-transparent text-neutral-content hover:bg-gray-700 focus:ring-secondary";
      break;
  }

  let sizeStyle = "";
  switch (size) {
    case 'sm':
      sizeStyle = "px-3 py-1.5 text-sm";
      break;
    case 'md':
      sizeStyle = "px-4 py-2 text-base";
      break;
    case 'lg':
      sizeStyle = "px-6 py-3 text-lg";
      break;
  }

  return (
    <button
      className={`${baseStyle} ${variantStyle} ${sizeStyle} ${isLoading ? 'opacity-75 cursor-not-allowed' : ''} ${className}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {isLoading ? (
        <div className="flex items-center justify-center">
          <svg className="animate-spin h-5 w-5 mr-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Processing...
        </div>
      ) : (
        children
      )}
    </button>
  );
};
