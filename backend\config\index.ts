import { z } from 'zod';

const configSchema = z.object({
  // Server Configuration
  PORT: z.string().default('3001'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Database Configuration
  REDIS_URL: z.string().default('redis://localhost:6379'),
  DATABASE_URL: z.string().optional(),
  INFLUXDB_URL: z.string().optional(),
  INFLUXDB_TOKEN: z.string().optional(),
  INFLUXDB_ORG: z.string().optional(),
  INFLUXDB_BUCKET: z.string().optional(),
  
  // Blockchain Configuration
  ETHEREUM_RPC_URL: z.string().default('https://eth-mainnet.alchemyapi.io/v2/your-api-key'),
  POLYGON_RPC_URL: z.string().default('https://polygon-mainnet.alchemyapi.io/v2/your-api-key'),
  BSC_RPC_URL: z.string().default('https://bsc-dataseed.binance.org/'),
  SOLANA_RPC_URL: z.string().default('https://api.mainnet-beta.solana.com'),
  
  // Private Keys (for transaction signing)
  PRIVATE_KEY: z.string().optional(),
  FLASHBOTS_PRIVATE_KEY: z.string().optional(),
  
  // External API Keys
  CHAINLINK_API_KEY: z.string().optional(),
  PYTH_API_KEY: z.string().optional(),
  COINGECKO_API_KEY: z.string().optional(),
  
  // Trading Configuration
  MIN_PROFIT_THRESHOLD: z.string().default('50'), // USD
  MAX_POSITION_SIZE: z.string().default('10000'), // USD
  MAX_SLIPPAGE: z.string().default('0.5'), // percentage
  GAS_PRICE_MULTIPLIER: z.string().default('1.1'),
  
  // Risk Management
  EMERGENCY_STOP: z.string().default('false'),
  MAX_DAILY_LOSS: z.string().default('1000'), // USD
  POSITION_SIZE_PERCENTAGE: z.string().default('2'), // percentage of total capital
  
  // Monitoring
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  ENABLE_METRICS: z.string().default('true'),
});

export type Config = z.infer<typeof configSchema>;

export const config: Config = configSchema.parse(process.env);

export default config;
