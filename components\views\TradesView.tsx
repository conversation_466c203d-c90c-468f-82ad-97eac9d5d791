
import React from 'react';
import { Trade, TradeStatus } from '../../types';
import { Card } from '../common/Card';
import { TableIcon } from '../icons';

interface TradesViewProps {
  trades: Trade[];
}

const getStatusColor = (status: TradeStatus) => {
  switch (status) {
    case TradeStatus.SUCCESS: return 'text-success';
    case TradeStatus.FAILED: return 'text-error';
    case TradeStatus.PENDING: return 'text-warning';
    default: return 'text-neutral-content';
  }
};

export const TradesView: React.FC<TradesViewProps> = ({ trades }) => {
  return (
    <Card title="Trade Log" className="max-h-[calc(100vh-10rem)] overflow-hidden flex flex-col">
      {trades.length === 0 ? (
         <div className="flex flex-col items-center justify-center h-full text-gray-500 py-10">
            <TableIcon className="w-16 h-16 mb-4" />
            <p className="text-xl">No trades executed yet.</p>
            <p>Completed trades will appear here.</p>
        </div>
      ) : (
      <div className="overflow-x-auto flex-grow">
        <table className="min-w-full divide-y divide-secondary">
          <thead className="bg-base-100 sticky top-0">
            <tr>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Timestamp</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Type</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Assets</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Network</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Profit</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Gas Fees</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
            </tr>
          </thead>
          <tbody className="bg-neutral divide-y divide-secondary overflow-y-auto">
            {trades.map((trade) => (
              <tr key={trade.id} className="hover:bg-secondary/30 transition-colors">
                <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">{new Date(trade.timestamp).toLocaleString()}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">{trade.type}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">{trade.assets.join('/')}</td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-neutral-content">{trade.network}</td>
                <td className={`px-4 py-3 whitespace-nowrap text-sm font-medium ${trade.executedProfit >= 0 ? 'text-success' : 'text-error'}`}>
                  {trade.executedProfit.toFixed(2)} USD
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-400">{trade.gasFees.toFixed(2)} USD</td>
                <td className={`px-4 py-3 whitespace-nowrap text-sm font-semibold ${getStatusColor(trade.status)}`}>{trade.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      )}
    </Card>
  );
};
