
import React from 'react';
import { Card } from '../common/Card';
import { CogIcon } from '../icons';

interface ConfigItemProps {
  category: string;
  settings: { name: string; value: string; description: string }[];
}

const ConfigCategory: React.FC<ConfigItemProps> = ({ category, settings }) => (
  <Card title={category} className="bg-neutral" titleClassName="text-lg">
    <ul className="space-y-3">
      {settings.map(setting => (
        <li key={setting.name} className="p-3 bg-base-100 rounded-md">
          <div className="flex justify-between items-center">
            <span className="font-medium text-neutral-content">{setting.name}</span>
            <span className="text-accent font-semibold">{setting.value}</span>
          </div>
          <p className="text-xs text-gray-400 mt-1">{setting.description}</p>
        </li>
      ))}
    </ul>
  </Card>
);

export const ConfigurationView: React.FC = () => {
  const generalSettings = [
    { name: "Bot Mode", value: "Live Trading", description: "Current operational mode (Live/Simulation)." },
    { name: "Minimum Profit Threshold", value: "$50 USD", description: "Minimum expected profit to consider an opportunity." },
    { name: "Flashbots Integration", value: "Enabled", description: "Utilizes Flashbots for private transaction submission." },
  ];

  const networkSettings = [
    { name: "Supported Blockchains", value: "Ethereum, Polygon, BSC", description: "Chains monitored for arbitrage." },
    { name: "RPC Endpoint (Ethereum)", value: "******** (Private Node)", description: "Primary RPC for Ethereum interaction." },
    { name: "Gas Price Strategy", value: "Dynamic (Fast)", description: "Strategy for setting gas prices for transactions." },
  ];
  
  const dataSettings = [
    { name: "Price Feed Aggregation", value: "VWAP (Multiple DEXs)", description: "Method for calculating composite price feeds." },
    { name: "Oracle Usage", value: "Chainlink, Pyth (for key assets)", description: "Decentralized oracles used for price verification." },
    { name: "Data Persistence", value: "Supabase (Trades), Redis (Cache)", description: "Storage for historical and real-time data." },
  ];


  return (
    <div className="space-y-6">
      <div className="flex items-center text-2xl font-semibold mb-4">
        <CogIcon className="w-8 h-8 mr-3 text-primary"/>
        Bot Configuration Overview
      </div>
      <p className="text-gray-400 mb-6">Displaying current operational parameters. Changes typically require backend updates or governance actions.</p>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <ConfigCategory category="General Settings" settings={generalSettings} />
        <ConfigCategory category="Network & Gas" settings={networkSettings} />
        <ConfigCategory category="Data & Oracles" settings={dataSettings} />
      </div>
    </div>
  );
};
