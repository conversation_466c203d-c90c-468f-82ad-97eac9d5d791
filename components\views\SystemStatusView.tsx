
import React from 'react';
import { SystemComponent, ComponentStatus, SystemComponentType } from '../../types';
import { Card } from '../common/Card';

interface SystemStatusViewProps {
  components: SystemComponent[];
}

const StatusIndicator: React.FC<{ status: ComponentStatus }> = ({ status }) => {
  let colorClass = '';
  let pulseClass = '';
  switch (status) {
    case ComponentStatus.ONLINE:
      colorClass = 'bg-success';
      break;
    case ComponentStatus.WARNING:
      colorClass = 'bg-warning';
      pulseClass = 'animate-pulse-fast';
      break;
    case ComponentStatus.OFFLINE:
      colorClass = 'bg-error';
      break;
    case ComponentStatus.DEGRADED:
      colorClass = 'bg-yellow-600'; // A darker yellow for degraded
      pulseClass = 'animate-pulse-fast';
      break;
    default:
      colorClass = 'bg-gray-500';
  }
  return <span className={`inline-block w-3 h-3 rounded-full mr-2 ${colorClass} ${pulseClass}`}></span>;
};

const groupComponentsByType = (components: SystemComponent[]) => {
  return components.reduce((acc, component) => {
    if (!acc[component.type]) {
      acc[component.type] = [];
    }
    acc[component.type].push(component);
    return acc;
  }, {} as Record<SystemComponentType, SystemComponent[]>);
};

export const SystemStatusView: React.FC<SystemStatusViewProps> = ({ components }) => {
  const groupedComponents = groupComponentsByType(components);

  return (
    <div className="space-y-6">
      <Card title="System Component Status">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(groupedComponents).map(([type, comps]) => (
            <Card key={type} title={type as string} className="bg-neutral" titleClassName="text-lg">
              <ul className="space-y-2">
                {comps.map(comp => (
                  <li key={comp.id} className="flex items-center justify-between p-2 bg-base-100 rounded">
                    <span className="text-sm text-neutral-content">{comp.name}</span>
                    <div className="flex items-center">
                      <StatusIndicator status={comp.status} />
                      <span className="text-xs text-gray-400">{comp.status}</span>
                    </div>
                  </li>
                ))}
              </ul>
            </Card>
          ))}
        </div>
      </Card>
       <Card title="System Interaction Diagram (Conceptual)">
          <p className="text-gray-400 mb-4">This is a conceptual representation. Refer to system documentation for the detailed Mermaid diagram.</p>
          <div className="bg-base-100 p-4 rounded-lg overflow-x-auto">
            <pre className="text-xs text-neutral-content whitespace-pre-wrap">
{`graph TD
    subgraph External Systems
        DEXs --- PF_Service
        BlockchainRPC --- PF_Service
        Flashbots --- EXEC_Service
    end
    subgraph Backend Services
        PF_Service[Price Feed] --> OD_Service[Opportunity Detection]
        OD_Service --> EXEC_Service[Execution Service]
        EXEC_Service --> RM_Service[Risk Management]
        EXEC_Service --> AL_Service[Analytics & Learning]
    end
    subgraph Data Layer
        Redis[Redis Cache] <--> PF_Service
        Redis <--> OD_Service
        Supabase[Supabase DB] <--> AL_Service
    end
    subgraph Smart Contracts
        SC_Executor[ArbitrageExecutor.sol] <--> EXEC_Service
        SC_Liquidity[LiquidityChecker.sol] <--> OD_Service
    end
    subgraph User Interface
        Frontend --> Backend Services
    end
`}
            </pre>
            <p className="mt-4 text-sm text-gray-500 italic">Note: This is a simplified text-based diagram. Full Mermaid rendering is typically done with a dedicated library.</p>
        </div>
      </Card>
    </div>
  );
};
