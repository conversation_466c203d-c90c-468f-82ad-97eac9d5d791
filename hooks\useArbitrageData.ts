
import { useState, useEffect, useCallback } from 'react';
import { Token, ArbitrageOpportunity, Trade, KPI, ArbitrageType, TradeStatus, SystemComponent, SystemComponentType, ComponentStatus } from '../types';

const INITIAL_TOKENS: Token[] = [
  { id: 'T1', name: 'Ether', symbol: 'ETH', address: '0x...', liquidity: 1500000, safetyScore: 95, isWhitelisted: true, network: 'Ethereum' },
  { id: 'T2', name: 'Wrapped Bitcoin', symbol: 'WBTC', address: '0x...', liquidity: 800000, safetyScore: 90, isWhitelisted: true, network: 'Ethereum' },
  { id: 'T3', name: 'USD Coin', symbol: 'USDC', address: '0x...', liquidity: 2500000, safetyScore: 98, isWhitelisted: true, network: 'Polygon' },
  { id: 'T4', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', symbol: 'BOG', address: '0x...', liquidity: 1000, safetyScore: 10, isWhitelisted: false, network: 'BSC' },
];

const INITIAL_KPIS: KPI = {
  totalProfit: 1250.75,
  roi: 15.2,
  winRate: 88.5,
  activeTrades: 2,
  dailyVolume: 75000,
};

const INITIAL_SYSTEM_COMPONENTS: SystemComponent[] = [
    { id: 'sc1', name: 'ArbitrageExecutor.sol', type: SystemComponentType.SMART_CONTRACT, status: ComponentStatus.ONLINE },
    { id: 'sc2', name: 'LiquidityChecker.sol', type: SystemComponentType.SMART_CONTRACT, status: ComponentStatus.ONLINE },
    { id: 'bs1', name: 'Price Feed Service', type: SystemComponentType.BACKEND_SERVICE, status: ComponentStatus.ONLINE },
    { id: 'bs2', name: 'Opportunity Detection', type: SystemComponentType.BACKEND_SERVICE, status: ComponentStatus.ONLINE },
    { id: 'bs3', name: 'Execution Service', type: SystemComponentType.BACKEND_SERVICE, status: ComponentStatus.WARNING },
    { id: 'dl1', name: 'Redis Cache', type: SystemComponentType.DATA_LAYER, status: ComponentStatus.ONLINE },
    { id: 'dl2', name: 'Supabase DB', type: SystemComponentType.DATA_LAYER, status: ComponentStatus.DEGRADED },
    { id: 'ext1', name: 'Flashbots Relay', type: SystemComponentType.EXTERNAL_INTEGRATION, status: ComponentStatus.ONLINE },
    { id: 'ext2', name: 'Chainlink Oracles', type: SystemComponentType.EXTERNAL_INTEGRATION, status: ComponentStatus.OFFLINE },
];


const generateRandomNumber = (min: number, max: number, decimals: number = 2) => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
};

const ALL_SYMBOLS = ['ETH', 'WBTC', 'USDC', 'DAI', 'UNI', 'AAVE', 'MATIC', 'SOL'];
const ALL_EXCHANGES = ['Uniswap', 'Sushiswap', 'Balancer', 'Curve', 'Quickswap', 'Raydium'];
const ALL_NETWORKS = ['Ethereum', 'Polygon', 'Solana', 'BSC'];

export const useArbitrageData = () => {
  const [tokens, setTokens] = useState<Token[]>(INITIAL_TOKENS);
  const [opportunities, setOpportunities] = useState<ArbitrageOpportunity[]>([]);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [kpis, setKpis] = useState<KPI>(INITIAL_KPIS);
  const [systemComponents, setSystemComponents] = useState<SystemComponent[]>(INITIAL_SYSTEM_COMPONENTS);
  const [isEmergencyStopActive, setIsEmergencyStopActive] = useState(false);

  const createRandomOpportunity = useCallback(() => {
    const typeValues = Object.values(ArbitrageType);
    const type = typeValues[Math.floor(Math.random() * typeValues.length)];
    const assets = [ALL_SYMBOLS[Math.floor(Math.random()*ALL_SYMBOLS.length)], ALL_SYMBOLS[Math.floor(Math.random()*ALL_SYMBOLS.length)]];
    if (type === ArbitrageType.TRIANGULAR) assets.push(ALL_SYMBOLS[Math.floor(Math.random()*ALL_SYMBOLS.length)]);
    
    const newOpportunity: ArbitrageOpportunity = {
      id: `OP-${Date.now()}-${Math.random().toString(16).slice(2,6)}`,
      type,
      assets: assets.filter((v, i, a) => a.indexOf(v) === i).slice(0, type === ArbitrageType.TRIANGULAR ? 3 : 2), // Ensure unique, correct count
      exchanges: [ALL_EXCHANGES[Math.floor(Math.random()*ALL_EXCHANGES.length)], ALL_EXCHANGES[Math.floor(Math.random()*ALL_EXCHANGES.length)]].filter((v,i,a) => a.indexOf(v)===i),
      potentialProfit: generateRandomNumber(10, 250),
      timestamp: Date.now(),
      network: ALL_NETWORKS[Math.floor(Math.random()*ALL_NETWORKS.length)]
    };
    setOpportunities(prev => [newOpportunity, ...prev.slice(0, 19)]);
  }, []);

  const simulateTradeExecution = useCallback(() => {
    if (isEmergencyStopActive) return;
    setOpportunities(prevOps => {
      if (prevOps.length > 0 && Math.random() > 0.5) { // Simulate trade execution
        const opportunityToExecute = prevOps[prevOps.length -1]; // Take oldest
        const success = Math.random() > 0.15; // 85% success rate
        const executedProfit = success ? generateRandomNumber(opportunityToExecute.potentialProfit * 0.8, opportunityToExecute.potentialProfit * 1.1) : 0;
        const gasFees = generateRandomNumber(5, 50);

        const newTrade: Trade = {
          id: `TRD-${opportunityToExecute.id}`,
          opportunityId: opportunityToExecute.id,
          type: opportunityToExecute.type,
          assets: opportunityToExecute.assets,
          exchanges: opportunityToExecute.exchanges,
          executedProfit: success ? executedProfit - gasFees : -gasFees,
          gasFees,
          status: success ? TradeStatus.SUCCESS : TradeStatus.FAILED,
          timestamp: Date.now(),
          network: opportunityToExecute.network,
        };
        setTrades(prevTrades => [newTrade, ...prevTrades.slice(0, 49)]);
        
        setKpis(prevKpis => ({
          ...prevKpis,
          totalProfit: prevKpis.totalProfit + (newTrade.executedProfit > 0 ? newTrade.executedProfit : 0) - (newTrade.executedProfit < 0 ? Math.abs(newTrade.executedProfit) : 0), // only add profit if positive
          winRate: ((prevKpis.winRate * (trades.length)) + (success ? 100 : 0)) / (trades.length + 1),
          activeTrades: Math.max(0, prevKpis.activeTrades + (success ? -1: 0)), // Assuming trade ends
          dailyVolume: prevKpis.dailyVolume + opportunityToExecute.potentialProfit, // rough estimate
          roi: prevKpis.totalProfit > 0 ? (prevKpis.totalProfit / (10000 + prevKpis.dailyVolume * 0.1)) * 100 : 0 // Very rough ROI
        }));
        return prevOps.slice(0, -1); // Remove executed opportunity
      }
      return prevOps;
    });
  }, [isEmergencyStopActive, trades.length]);

  const updateSystemComponentsStatus = useCallback(() => {
    setSystemComponents(prevComponents =>
      prevComponents.map(comp => {
        const statuses = Object.values(ComponentStatus);
        // Make offline/degraded less frequent
        const randomFactor = Math.random();
        let newStatus = comp.status;
        if (randomFactor < 0.02) { // 2% chance to change status
            newStatus = statuses[Math.floor(Math.random() * statuses.length)];
        } else if (randomFactor < 0.1 && (comp.status === ComponentStatus.OFFLINE || comp.status === ComponentStatus.DEGRADED)) { // 10% chance to recover if offline/degraded
            newStatus = ComponentStatus.ONLINE;
        }
        return { ...comp, status: newStatus };
      })
    );
  }, []);
  
  useEffect(() => {
    const opportunityInterval = setInterval(createRandomOpportunity, 3000); // New opportunity every 3s
    const tradeInterval = setInterval(simulateTradeExecution, 5000); // Simulate trade every 5s
    const kpiInterval = setInterval(() => { // Random fluctuation for active trades
        setKpis(prev => ({...prev, activeTrades: Math.max(0, prev.activeTrades + (Math.random() > 0.5 ? 1 : -1))}))
    }, 7000);
    const systemStatusInterval = setInterval(updateSystemComponentsStatus, 10000); // Update system statuses every 10s


    // eslint-disable-next-line react-hooks/exhaustive-deps
    return () => {
      clearInterval(opportunityInterval);
      clearInterval(tradeInterval);
      clearInterval(kpiInterval);
      clearInterval(systemStatusInterval);
    };
  }, [createRandomOpportunity, simulateTradeExecution, updateSystemComponentsStatus]); // Added dependencies

  const toggleEmergencyStop = () => {
    setIsEmergencyStopActive(prev => !prev);
  };

  return { tokens, opportunities, trades, kpis, systemComponents, isEmergencyStopActive, toggleEmergencyStop, setTokens };
};
