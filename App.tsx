
import React, { useState } from 'react';
import { Layout } from './components/Layout';
import { DashboardView } from './components/views/DashboardView';
import { OpportunitiesView } from './components/views/OpportunitiesView';
import { TradesView } from './components/views/TradesView';
import { TokensView } from './components/views/TokensView';
import { SystemStatusView } from './components/views/SystemStatusView';
import { StrategyInsightsView } from './components/views/StrategyInsightsView';
import { RiskManagementView } from './components/views/RiskManagementView';
import { ConfigurationView } from './components/views/ConfigurationView';
import { useArbitrageData } from './hooks/useArbitrageData';
import { ViewId } from './types';

const App: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewId>(ViewId.DASHBOARD);
  const { 
    tokens, 
    opportunities, 
    trades, 
    kpis, 
    systemComponents, 
    isEmergencyStopActive, 
    toggleEmergencyStop,
    setTokens
  } = useArbitrageData();

  const renderView = () => {
    switch (currentView) {
      case ViewId.DASHBOARD:
        return <DashboardView kpis={kpis} recentTrades={trades} recentOpportunities={opportunities} />;
      case ViewId.OPPORTUNITIES:
        return <OpportunitiesView opportunities={opportunities} />;
      case ViewId.TRADES:
        return <TradesView trades={trades} />;
      case ViewId.TOKENS:
        return <TokensView tokens={tokens} setTokens={setTokens} />;
      case ViewId.SYSTEM_STATUS:
        return <SystemStatusView components={systemComponents} />;
      case ViewId.STRATEGY_INSIGHTS:
        return <StrategyInsightsView />;
      case ViewId.RISK_MANAGEMENT:
        return <RiskManagementView isEmergencyStopActive={isEmergencyStopActive} onToggleEmergencyStop={toggleEmergencyStop} />;
      case ViewId.CONFIGURATION:
        return <ConfigurationView />;
      default:
        return <DashboardView kpis={kpis} recentTrades={trades} recentOpportunities={opportunities} />;
    }
  };

  return (
    <Layout currentView={currentView} onNavigate={setCurrentView}>
      {renderView()}
    </Layout>
  );
};

export default App;
