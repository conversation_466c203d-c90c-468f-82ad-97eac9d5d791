# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
REDIS_URL=redis://localhost:6379
DATABASE_URL=postgresql://username:password@localhost:5432/mev_arbitrage_bot
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=your-org
INFLUXDB_BUCKET=mev-arbitrage-data

# Blockchain Configuration
ETHEREUM_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-api-key
POLYGON_RPC_URL=https://polygon-mainnet.alchemyapi.io/v2/your-api-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Private Keys (NEVER commit real private keys)
PRIVATE_KEY=your-private-key-here
FLASHBOTS_PRIVATE_KEY=your-flashbots-private-key

# External API Keys
CHAINLINK_API_KEY=your-chainlink-api-key
PYTH_API_KEY=your-pyth-api-key
COINGECKO_API_KEY=your-coingecko-api-key

# Trading Configuration
MIN_PROFIT_THRESHOLD=50
MAX_POSITION_SIZE=10000
MAX_SLIPPAGE=0.5
GAS_PRICE_MULTIPLIER=1.1

# Risk Management
EMERGENCY_STOP=false
MAX_DAILY_LOSS=1000
POSITION_SIZE_PERCENTAGE=2

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true
