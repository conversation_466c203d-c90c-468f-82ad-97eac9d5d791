
import React from 'react';
import { ArbitrageOpportunity } from '../../types';
import { Card } from '../common/Card';
import { LightBulbIcon } from '../icons';

interface OpportunitiesViewProps {
  opportunities: ArbitrageOpportunity[];
}

const OpportunityItem: React.FC<{ opportunity: ArbitrageOpportunity }> = ({ opportunity }) => (
  <div className="p-3 bg-neutral hover:bg-secondary rounded-md transition-colors">
    <div className="flex justify-between items-center mb-1">
      <span className={`text-sm font-semibold px-2 py-0.5 rounded-full 
        ${opportunity.type === 'Intra-chain' ? 'bg-blue-500 text-blue-100' : 
          opportunity.type === 'Cross-chain' ? 'bg-purple-500 text-purple-100' : 
          'bg-yellow-500 text-yellow-100'}`}>
        {opportunity.type}
      </span>
      <span className="text-xs text-gray-400">{new Date(opportunity.timestamp).toLocaleTimeString()} on {opportunity.network}</span>
    </div>
    <p className="text-md font-medium text-neutral-content">
      {opportunity.assets.join(' / ')} on {opportunity.exchanges.join(' & ')}
    </p>
    <p className="text-lg font-bold text-success">${opportunity.potentialProfit.toFixed(2)}</p>
  </div>
);

export const OpportunitiesView: React.FC<OpportunitiesViewProps> = ({ opportunities }) => {
  return (
    <Card title="Live Arbitrage Opportunities" className="max-h-[calc(100vh-10rem)] overflow-hidden flex flex-col">
      {opportunities.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-gray-500 py-10">
            <LightBulbIcon className="w-16 h-16 mb-4" />
            <p className="text-xl">No opportunities detected currently.</p>
            <p>The system is actively scanning...</p>
        </div>
      ) : (
        <div className="space-y-3 overflow-y-auto pr-2 flex-grow">
          {opportunities.map(op => (
            <OpportunityItem key={op.id} opportunity={op} />
          ))}
        </div>
      )}
    </Card>
  );
};
