
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEV Arbitrage Bot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#3B82F6',      // Blue-500
                secondary: '#374151',    // Cool Gray-700 (used as hover, borders)
                accent: '#F59E0B',       // Amber-500
                neutral: '#1F2937',      // Cool Gray-800 (main background)
                'base-100': '#111827',   // Cool Gray-900 (card backgrounds)
                info: '#2563EB',         // Blue-600
                success: '#10B981',      // Emerald-500
                warning: '#FBBF24',      // Amber-400
                error: '#EF4444',        // Red-500
                'neutral-content-default': '#D1D5DB', // Cool Gray-300 for text on neutral bg
              },
              animation: {
                'pulse-fast': 'pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              }
            }
          },
          plugins: [
            function({ addUtilities, theme }) {
              const newUtilities = {
                '.text-neutral-content': {
                  color: theme('colors.neutral-content-default'),
                },
              }
              addUtilities(newUtilities, ['responsive', 'hover'])
            }
          ],
        }
      </script>
      <style type="text/tailwindcss">
        @layer base {
          html, body {
            @apply bg-neutral text-neutral-content h-full;
          }
        }
      </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0",
    "recharts": "https://esm.sh/recharts@^2.15.3"
  }
}
</script>
</head>
<body>
    <div id="root" class="h-full"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>
<link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
