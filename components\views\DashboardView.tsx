
import React from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { KPI, Trade, ArbitrageOpportunity } from '../../types';
import { Card } from '../common/Card';

interface DashboardViewProps {
  kpis: KPI;
  recentTrades: Trade[];
  recentOpportunities: ArbitrageOpportunity[];
}

const KPICard: React.FC<{ title: string; value: string | number; unit?: string; color?: string }> = ({ title, value, unit, color = "text-accent" }) => (
  <Card className="text-center">
    <h3 className="text-sm font-medium text-gray-400 uppercase">{title}</h3>
    <p className={`text-3xl font-bold ${color}`}>{typeof value === 'number' ? value.toLocaleString() : value}{unit && <span className="text-lg ml-1">{unit}</span>}</p>
  </Card>
);


export const DashboardView: React.FC<DashboardViewProps> = ({ kpis, recentTrades, recentOpportunities }) => {
  const profitData = recentTrades.slice(0, 20).reverse().map(trade => ({
    name: new Date(trade.timestamp).toLocaleTimeString(),
    profit: trade.executedProfit,
  }));

  const opportunityTypesData = Object.values(recentOpportunities.reduce((acc, curr) => {
    acc[curr.type] = (acc[curr.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>)).map((count, index) => ({
    name: Object.keys(recentOpportunities.reduce((acc, curr) => {
        acc[curr.type] = (acc[curr.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>))[index],
    count
  }));


  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard title="Total Profit" value={kpis.totalProfit.toFixed(2)} unit="$" color="text-success" />
        <KPICard title="ROI" value={kpis.roi.toFixed(2)} unit="%" />
        <KPICard title="Win Rate" value={kpis.winRate.toFixed(2)} unit="%" color={kpis.winRate > 80 ? "text-success" : "text-warning"} />
        <KPICard title="Active Trades" value={kpis.activeTrades} />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Recent Profit Trend (Last 20 Trades)">
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={profitData}>
              <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.2} />
              <XAxis dataKey="name" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #4B5563', borderRadius: '0.5rem' }}
                itemStyle={{ color: '#D1D5DB' }}
              />
              <Legend />
              <Line type="monotone" dataKey="profit" stroke="#10B981" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
            </LineChart>
          </ResponsiveContainer>
        </Card>
        <Card title="Opportunity Types (Last 20)">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={opportunityTypesData}>
                <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.2}/>
                <XAxis dataKey="name" stroke="#9CA3AF" />
                <YAxis stroke="#9CA3AF" />
                <Tooltip 
                    contentStyle={{ backgroundColor: '#1F2937', border: '1px solid #4B5563', borderRadius: '0.5rem' }}
                    itemStyle={{ color: '#D1D5DB' }}
                />
                <Legend />
                <Bar dataKey="count" fill="#F59E0B" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>
    </div>
  );
};
